<mxfile host="65bd71144e">
    <diagram id="gic_of_init_full" name="GIC初始化流程图完整版">
        <mxGraphModel dx="1454" dy="940" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="800" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title1" value="链接脚本中定义段" style="text;html=1;fontSize=14;fontStyle=1;strokeColor=none;fillColor=none;align=center;" parent="1" vertex="1">
                    <mxGeometry x="220" y="10" width="200" height="24" as="geometry"/>
                </mxCell>
                <mxCell id="title2" value="声明结构，并将该结构放置到__irqchip_of_table段" style="text;html=1;fontSize=14;fontStyle=1;strokeColor=none;fillColor=none;align=center;" parent="1" vertex="1">
                    <mxGeometry x="600" y="10" width="350" height="24" as="geometry"/>
                </mxCell>
                <mxCell id="irqchip_table" value="vm linux.lds: __irqchip_of_table" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="220" y="40" width="200" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="irqchip_decl" value="IRQCHIP_DECLARE(gic_400, &#39;arm,gic-400&#39;, gic_of_init)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="600" y="40" width="350" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="of_irq_init" value="of_irq_init(__irqchip_of_table)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#fbc02d;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="220" y="120" width="200" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="gic_of_init" value="gic_of_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;strokeWidth=2;" parent="1" vertex="1">
                    <mxGeometry x="480" y="220" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="start_kernel" value="start_kernel" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3eafc;strokeColor=#607d8b;" parent="1" vertex="1">
                    <mxGeometry x="40" y="60" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="init_irq_stacks" value="init_irq_stacks" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3eafc;strokeColor=#607d8b;" parent="1" vertex="1">
                    <mxGeometry x="40" y="100" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="init_IRQ" value="init_IRQ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3eafc;strokeColor=#607d8b;" parent="1" vertex="1">
                    <mxGeometry x="40" y="140" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="irqchip_init" value="irqchip_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3eafc;strokeColor=#607d8b;" parent="1" vertex="1">
                    <mxGeometry x="40" y="180" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="desc_parse" value="desc-&gt;irq_init_cb=match-&gt;data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;" parent="1" vertex="1">
                    <mxGeometry x="220" y="180" width="200" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="desc_call" value="desc-&gt;irq_init_cb(desc-&gt;dev, desc-&gt;interrupt_parent)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;" parent="1" vertex="1">
                    <mxGeometry x="210" y="280" width="200" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="gic_of_setup" value="gic_of_setup" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" parent="1" vertex="1">
                    <mxGeometry x="650" y="220" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="gic_init_bases" value="gic_init_bases" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" parent="1" vertex="1">
                    <mxGeometry x="800" y="220" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="gic_init_chip" value="gic_init_chip" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" parent="1" vertex="1">
                    <mxGeometry x="950" y="220" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="gic_irq_domain_hierarchy_ops" value="gic_irq_domain_hierarchy_ops" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;" parent="1" vertex="1">
                    <mxGeometry x="1100" y="220" width="180" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="gic_cpu_init" value="gic_cpu_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#8e24aa;" parent="1" vertex="1">
                    <mxGeometry x="800" y="270" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="gic_pm_init" value="gic_pm_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#8e24aa;" parent="1" vertex="1">
                    <mxGeometry x="950" y="270" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="gic_raise_softirq" value="gic_raise_softirq" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#fbc02d;" parent="1" vertex="1">
                    <mxGeometry x="650" y="270" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="gic_starting_cpu" value="gic_starting_cpu" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#fbc02d;" parent="1" vertex="1">
                    <mxGeometry x="650" y="320" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="gic_handle_irq" value="gic_handle_irq" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#fbc02d;" parent="1" vertex="1">
                    <mxGeometry x="800" y="320" width="120" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="irq_domain_create_linear" value="irq_domain_create_linear( gic_irq_domain_hierarchy_ops )" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;" parent="1" vertex="1">
                    <mxGeometry x="1100" y="270" width="180" height="32" as="geometry"/>
                </mxCell>
                <mxCell id="a1" style="edgeStyle=orthogonalEdgeStyle;dashed=1;dashPattern=5 5;html=1;strokeColor=#d32f2f;endArrow=block;" parent="1" source="irqchip_table" target="of_irq_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a2" style="edgeStyle=orthogonalEdgeStyle;dashed=1;dashPattern=5 5;html=1;strokeColor=#1976d2;endArrow=block;" parent="1" source="irqchip_decl" target="gic_of_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a3" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#616161;endArrow=block;" parent="1" source="of_irq_init" target="desc_parse" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a4" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#616161;endArrow=block;" parent="1" source="desc_parse" target="desc_call" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a5" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#616161;endArrow=block;" parent="1" source="desc_call" target="gic_of_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a6" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#607d8b;dashed=1;dashPattern=5 5;endArrow=block;" parent="1" source="start_kernel" target="init_irq_stacks" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a7" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#607d8b;dashed=1;dashPattern=5 5;endArrow=block;" parent="1" source="init_irq_stacks" target="init_IRQ" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a8" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#607d8b;dashed=1;dashPattern=5 5;endArrow=block;" parent="1" source="init_IRQ" target="irqchip_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a9" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#607d8b;dashed=1;dashPattern=5 5;endArrow=block;" parent="1" source="irqchip_init" target="of_irq_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a10" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#f57c00;endArrow=block;" parent="1" source="gic_of_init" target="gic_of_setup" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a11" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" parent="1" source="gic_of_setup" target="gic_init_bases" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a12" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" parent="1" source="gic_init_bases" target="gic_init_chip" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a13" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" parent="1" source="gic_init_chip" target="gic_irq_domain_hierarchy_ops" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a14" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" parent="1" source="gic_init_bases" target="gic_cpu_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a15" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" parent="1" source="gic_init_bases" target="gic_pm_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a16" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#fbc02d;endArrow=block;" parent="1" source="gic_of_setup" target="gic_raise_softirq" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a17" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#fbc02d;endArrow=block;" parent="1" source="gic_of_setup" target="gic_starting_cpu" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a18" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#fbc02d;endArrow=block;" parent="1" source="gic_of_setup" target="gic_handle_irq" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="a19" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;dashed=1;dashPattern=5 5;endArrow=block;" parent="1" source="gic_irq_domain_hierarchy_ops" target="irq_domain_create_linear" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="note1" value="内核入口" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="10" y="30" width="80" height="24" as="geometry"/>
                </mxCell>
                <mxCell id="note2" value="解析节点，获取回调函数" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="220" y="160" width="180" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note3" value="调用回调函数：gic_of_init" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="220" y="260" width="180" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note4" value="设置GIC的地址信息" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="650" y="200" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note5" value="设置SMP核间交互的回调函数，用于IPI" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="650" y="250" width="200" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note6" value="设置CPU热插拔时GIC的回调函数" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="650" y="300" width="200" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note7" value="设置中断handler，异常处理入口" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="800" y="300" width="200" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note8" value="初始化irq_chip结构" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="950" y="200" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note9" value="分配irq_domain，并初始化domain_ops结构体" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="1100" y="200" width="180" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note10" value="初始化GIC Distributor" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="800" y="250" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note11" value="初始化GIC CPU Interface" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="800" y="300" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="note12" value="初始化GIC的电源管理模块，用于Suspend/Resume" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=left;" parent="1" vertex="1">
                    <mxGeometry x="950" y="300" width="200" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="footer" value="公众号：LoyenWang" style="text;html=1;fontSize=12;strokeColor=none;fillColor=none;align=right;" parent="1" vertex="1">
                    <mxGeometry x="1200" y="760" width="180" height="24" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
<mxfile host="65bd71144e">
    <diagram id="mermaid-graph" name="Page-1">
        <mxGraphModel dx="652" dy="821" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="400" math="0" shadow="1">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="A" value="赶工/降本决策" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#1890ff;fontSize=16;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="142" y="130" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="B" value="增加作业强度" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#1890ff;fontSize=16;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="142" y="10" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="C" value="简化程序/廉价替代" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#1890ff;fontSize=16;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="120" y="240" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="D" value="操作失误率/安全风险上升" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#faad14;fontSize=16;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="317" y="10" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="E" value="施工质量下降" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#faad14;fontSize=16;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="318" y="240" width="160" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="F" value="质量问题/安全隐患出现" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#cc0000;strokeWidth=2;fontSize=16;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="327" y="120" width="140" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="G" value="返工/停工整改" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#cc0000;fontSize=16;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="528" y="130" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="H" value="实际进度延误" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f7ff;strokeColor=#1890ff;fontSize=16;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="528" y="11" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="I" value="实际成本激增" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffbe6;strokeColor=#faad14;fontSize=16;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="709" y="130" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="J" value="进度/成本压力更大" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#cc0000;strokeWidth=2;fontSize=16;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="699" y="1" width="140" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="e1" style="endArrow=block;endFill=1;strokeColor=#1890ff;" parent="1" source="A" target="B" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e2" style="endArrow=block;endFill=1;strokeColor=#1890ff;" parent="1" source="A" target="C" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e3" style="endArrow=block;endFill=1;strokeColor=#faad14;" parent="1" source="B" target="D" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e4" style="endArrow=block;endFill=1;strokeColor=#faad14;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="C" target="E" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="296" y="270" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="e5" style="endArrow=block;endFill=1;strokeColor=#cc0000;" parent="1" source="D" target="F" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e6" style="endArrow=block;endFill=1;strokeColor=#cc0000;" parent="1" source="E" target="F" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e7" style="endArrow=block;endFill=1;strokeColor=#cc0000;" parent="1" source="F" target="G" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e8" style="endArrow=block;endFill=1;strokeColor=#1890ff;" parent="1" source="G" target="H" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e9" style="endArrow=block;endFill=1;strokeColor=#faad14;" parent="1" source="G" target="I" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e10" style="endArrow=block;endFill=1;strokeColor=#cc0000;" parent="1" source="H" target="J" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e11" style="endArrow=block;endFill=1;strokeColor=#cc0000;" parent="1" source="I" target="J" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e12" style="endArrow=block;endFill=1;strokeColor=#cc0000;rounded=0;curved=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="J" target="A" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="950" y="160"/>
                            <mxPoint x="620" y="340"/>
                            <mxPoint x="130" y="350"/>
                            <mxPoint x="100" y="210"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
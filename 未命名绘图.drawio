<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="27.1.6">
  <diagram name="第 1 页" id="aaPEv2ZHV2enTBVx8O8U">
    <mxGraphModel dx="1489" dy="883" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="DqEVbXmUAKFzO1BlppYC-2" value="回顾与检验" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="278" y="2253" width="898" height="876" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-3" value="执行方案" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="238" y="1547" width="950" height="656" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-4" value="拟定方案" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="170" y="1059" width="1109" height="408" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-5" value="理解题目" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="358" y="50" width="694" height="967" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-6" value="未知量: a,b,c关系" style="rhombus;strokeWidth=2;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="604" y="179" width="182" height="182" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-7" value="开始: 比较a,b,c大小" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="593" y="75" width="204" height="54" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-8" value="已知: a,b,c的复杂表达式" style="rhombus;strokeWidth=2;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="580" y="411" width="230" height="230" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-9" value="条件: a,b,c的定义式" style="rhombus;strokeWidth=2;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="596" y="690" width="198" height="198" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-10" value="目标: 化简a,b,c,再比较" style="rounded=1;arcSize=20;strokeWidth=2" parent="1" vertex="1">
          <mxGeometry x="597" y="938" width="196" height="54" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-11" value="观察a: 2021×2022-2021²&#xa;有公因式2021" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="311" y="1122" width="233" height="78" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-12" value="计划a: 提取公因式" style="rounded=1;arcSize=20;strokeWidth=2" parent="1" vertex="1">
          <mxGeometry x="347" y="1290" width="161" height="54" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-13" value="观察b: 1013×1008-1012×1007&#xa;数字两两相邻" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="594" y="1114" width="260" height="102" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-14" value="计划b: 变量代换&#xa;令x=1012, y=1007" style="rounded=1;arcSize=20;strokeWidth=2" parent="1" vertex="1">
          <mxGeometry x="646" y="1278" width="157" height="78" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-15" value="观察c: √(2019²+2020+2021)&#xa;根号加连续整数" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="904" y="1118" width="255" height="78" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-16" value="计划c: 变量代换和完全平方公式&#xa;令n=2020" style="rounded=1;arcSize=20;strokeWidth=2" parent="1" vertex="1">
          <mxGeometry x="917" y="1266" width="230" height="102" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-17" value="形成总策略" style="rounded=1;arcSize=20;strokeWidth=2" parent="1" vertex="1">
          <mxGeometry x="655" y="1413" width="110" height="54" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-18" value="执行a: 2021乘(2022-2021)=2021" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="273" y="1572" width="260" height="78" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-19" value="执行b: (x+1)乘(y+1)-xy=x+y+1=2020" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="583" y="1572" width="260" height="78" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-20" value="执行c: √((n-1)²+n+(n+1))=√(n²+2)" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="893" y="1572" width="260" height="78" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-21" value="得出初步结果&#xa;a=2021, b=2020, c=√(2020²+2)" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="593" y="1700" width="260" height="102" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-22" value="比较大小" style="rhombus;strokeWidth=2;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="665" y="1852" width="118" height="118" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-23" value="2020 &amp;lt; √(2020²+2) &amp;lt; 2021" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="604" y="2014" width="241" height="54" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-24" value="最终结果: b &amp;lt; c &amp;lt; a" style="rounded=1;arcSize=20;strokeWidth=2" parent="1" vertex="1">
          <mxGeometry x="641" y="2124" width="166" height="54" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-25" value="结果可靠吗?" style="rhombus;strokeWidth=2;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="654" y="2278" width="140" height="140" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-26" value="有其他方法吗?&#xa;对b用其他变量代换" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="626" y="2468" width="197" height="78" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-27" value="能归纳出模式吗?" style="rhombus;strokeWidth=2;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="638" y="2596" width="172" height="172" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-28" value="模式a: n×(n+1)-n²=n" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="316" y="2812" width="204" height="54" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-29" value="模式b: (x+1)×(y+1)-xy=x+y+1" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="571" y="2800" width="260" height="78" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-30" value="模式c: √((n-1)²+n+(n+1))=√(n²+2)" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="881" y="2800" width="260" height="78" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-31" value="深化理解, 形成可复用知识" style="rounded=1;arcSize=20;strokeWidth=2" parent="1" vertex="1">
          <mxGeometry x="602" y="2928" width="217" height="54" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-32" value="结束" style="whiteSpace=wrap;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="664" y="3032" width="92" height="54" as="geometry" />
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-33" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-7" target="DqEVbXmUAKFzO1BlppYC-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-34" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-6" target="DqEVbXmUAKFzO1BlppYC-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-35" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-8" target="DqEVbXmUAKFzO1BlppYC-9" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-36" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-9" target="DqEVbXmUAKFzO1BlppYC-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-37" value="a" style="curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.85;entryX=0.5;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-10" target="DqEVbXmUAKFzO1BlppYC-11" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="428" y="1017" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-38" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1.01;entryX=0.5;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-11" target="DqEVbXmUAKFzO1BlppYC-12" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-39" value="b" style="curved=1;startArrow=none;endArrow=block;exitX=0.58;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-10" target="DqEVbXmUAKFzO1BlppYC-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="724" y="1017" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-40" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-13" target="DqEVbXmUAKFzO1BlppYC-14" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-41" value="c" style="curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.78;entryX=0.5;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-10" target="DqEVbXmUAKFzO1BlppYC-15" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1032" y="1017" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-42" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1.01;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-15" target="DqEVbXmUAKFzO1BlppYC-16" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-43" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1.01;entryX=0;entryY=0.31;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-12" target="DqEVbXmUAKFzO1BlppYC-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="428" y="1393" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-44" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1.01;entryX=0.61;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-14" target="DqEVbXmUAKFzO1BlppYC-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="724" y="1393" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-45" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=1;entryY=0.35;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-16" target="DqEVbXmUAKFzO1BlppYC-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1032" y="1393" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-46" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.69;entryX=0.5;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-17" target="DqEVbXmUAKFzO1BlppYC-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="403" y="1497" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-47" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.55;exitY=1.01;entryX=0.5;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-17" target="DqEVbXmUAKFzO1BlppYC-19" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="713" y="1497" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-48" value="" style="curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.67;entryX=0.5;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-17" target="DqEVbXmUAKFzO1BlppYC-20" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1023" y="1497" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-49" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1.01;entryX=0;entryY=0.2;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-18" target="DqEVbXmUAKFzO1BlppYC-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="403" y="1675" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-50" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1.01;entryX=0.47;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-19" target="DqEVbXmUAKFzO1BlppYC-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="713" y="1675" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-51" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1.01;entryX=1;entryY=0.18;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-20" target="DqEVbXmUAKFzO1BlppYC-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1023" y="1675" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-52" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-21" target="DqEVbXmUAKFzO1BlppYC-22" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-53" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-22" target="DqEVbXmUAKFzO1BlppYC-23" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-54" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1.01;entryX=0.5;entryY=0.01;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-23" target="DqEVbXmUAKFzO1BlppYC-24" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-55" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1.01;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-24" target="DqEVbXmUAKFzO1BlppYC-25" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-56" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-25" target="DqEVbXmUAKFzO1BlppYC-26" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-57" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-26" target="DqEVbXmUAKFzO1BlppYC-27" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-58" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.68;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-27" target="DqEVbXmUAKFzO1BlppYC-28" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="418" y="2793" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-59" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.39;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-27" target="DqEVbXmUAKFzO1BlppYC-29" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="701" y="2793" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-60" value="" style="curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.7;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-27" target="DqEVbXmUAKFzO1BlppYC-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1011" y="2793" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-61" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0;entryY=0.15;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-28" target="DqEVbXmUAKFzO1BlppYC-31" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="418" y="2903" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-62" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.48;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-29" target="DqEVbXmUAKFzO1BlppYC-31" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="701" y="2903" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-63" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=1;entryY=0.16;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-30" target="DqEVbXmUAKFzO1BlppYC-31" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1011" y="2903" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="DqEVbXmUAKFzO1BlppYC-64" value="" style="curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;" parent="1" source="DqEVbXmUAKFzO1BlppYC-31" target="DqEVbXmUAKFzO1BlppYC-32" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

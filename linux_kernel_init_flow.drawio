<mxfile host="65bd71144e">
    <diagram name="Linux内核初始化流程" id="linux-kernel-init">
        <mxGraphModel dx="1277" dy="873" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title1" value="链接脚本中定义段" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="80" y="20" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="title2" value="声明结构，并将这些结构放置到__irqchip_of_table段中" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="500" y="20" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="start_kernel" value="start_kernel" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="20" y="80" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="init_IRQ" value="init_IRQ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="20" y="160" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="irqchip_init" value="irqchip_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="130" y="200" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="of_irq_init" value="of_irq_init(__irqchip_of_table)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="20" y="280" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="vmlinux_lds" value="vmlinux.lds __irqchip_of_table" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="60" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="init_irq_stacks" value="init_irq_stacks" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="130" y="120" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_of_init" value="gic_of_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="350" y="160" width="80" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_init_bases" value="__gic_init_bases" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="350" y="220" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="irqchip_declare" value="IRQCHIP_DECLARE(gic_400, &quot;arm,gic-400&quot;, gic_of_init)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="500" y="60" width="280" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="set_smp_cross_call" value="set_smp_cross_call(gic_raise_softirq)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="580" y="120" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_raise_softirq" value="gic_raise_softirq" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="820" y="120" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="cpuhp_setup_state" value="cpuhp_setup_state_nocalls(gic_starting_cpu)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="580" y="170" width="220" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_starting_cpu" value="gic_starting_cpu" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="820" y="170" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="set_handle_irq" value="set_handle_irq(gic_handle_irq)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="580" y="220" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_handle_irq" value="gic_handle_irq" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="820" y="220" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_init_chip" value="gic_init_chip" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="580" y="280" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_irq_domain_hierarchy_ops" value="gic_irq_domain_hierarchy_ops" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="820" y="280" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="irq_domain_create_linear" value="irq_domain_create_linear(gic_irq_domain_hierarchy_ops)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="680" y="320" width="280" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_dist_init" value="gic_dist_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="680" y="380" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_cpu_init" value="gic_cpu_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="680" y="420" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="gic_pm_init" value="gic_pm_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;autosize=1;resizeWidth=1;" parent="1" vertex="1">
                    <mxGeometry x="680" y="460" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="comment1" value="内核入口" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="20" y="60" width="60" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="comment2" value="为每个CPU分配中断栈" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="240" y="125" width="100" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="comment4" value="解析节点，获取回调函数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="130" y="320" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="comment5" value="调用回调函数：gic_of_init" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="130" y="360" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="comment7" value="设置SMP核间交互的调度函数，用于IPI" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="800" y="100" width="160" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="comment8" value="设置CPU热插拔GIC的回调函数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="820" y="150" width="140" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="comment9" value="设置中断处理handler，异常处理的入口" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="780" y="200" width="180" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="comment10" value="初始化irq_chip结构" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="580" y="260" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="comment11" value="分配irq_domain，并初始化irq_domain_ops结构体" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="970" y="330" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="comment12" value="初始化GIC Distributor" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="790" y="385" width="120" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="comment13" value="初始化GIC CPU Interface" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="790" y="425" width="140" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="comment14" value="初始化GIC的电源管理模块，用于Suspend/Resume" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="790" y="465" width="200" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="author" value="公众号：LoyenWang" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="400" y="520" width="150" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="edge1" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="start_kernel" target="init_IRQ" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge2" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="init_IRQ" target="init_irq_stacks" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge3" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="init_IRQ" target="irqchip_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge4" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="irqchip_init" target="of_irq_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge5" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff0000;strokeWidth=2;dashed=1;curved=1;" parent="1" source="vmlinux_lds" target="irqchip_declare" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge6" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff0000;strokeWidth=2;dashed=1;curved=1;" parent="1" source="of_irq_init" target="vmlinux_lds" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge7" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="gic_of_init" target="gic_init_bases" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="label_param" value="传入参数" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;" parent="edge7" vertex="1" connectable="0">
                    <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge8" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="gic_init_bases" target="set_smp_cross_call" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge9" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff6600;curved=1;" parent="1" source="set_smp_cross_call" target="gic_raise_softirq" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="label_call" value="调用" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;" parent="edge9" vertex="1" connectable="0">
                    <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge10" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="gic_init_bases" target="cpuhp_setup_state" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge11" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff6600;curved=1;" parent="1" source="cpuhp_setup_state" target="gic_starting_cpu" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge12" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="gic_init_bases" target="set_handle_irq" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge13" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff6600;curved=1;" parent="1" source="set_handle_irq" target="gic_handle_irq" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge14" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="gic_init_bases" target="gic_init_chip" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="label_init" value="初始化" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=10;" parent="edge14" vertex="1" connectable="0">
                    <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="edge15" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff6600;curved=1;" parent="1" source="gic_init_chip" target="gic_irq_domain_hierarchy_ops" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge16" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff6600;curved=1;" parent="1" source="gic_irq_domain_hierarchy_ops" target="irq_domain_create_linear" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge17" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="gic_init_bases" target="gic_dist_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge18" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="gic_init_bases" target="gic_cpu_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="edge19" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;curved=1;" parent="1" source="gic_init_bases" target="gic_pm_init" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
<mxfile host="app.diagrams.net">
  <diagram id="MLTrainingFlow" name="机器学习训练流程图">
    <mxGraphModel dx="1200" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- Data Preparation -->
        <mxCell id="prepBox" value="Data Preparation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1565c0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="220" height="220" as="geometry" />
        </mxCell>
        <mxCell id="prep1" value="Load CIFAR-10 Data<br>from Files" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1565c0;" vertex="1" parent="prepBox">
          <mxGeometry x="30" y="20" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="prep2" value="Apply Transformations<br>(Crop, Flip, Normalize)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1565c0;" vertex="1" parent="prepBox">
          <mxGeometry x="30" y="70" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="prep3" value="Create DataLoaders<br>(Train/Test)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1565c0;" vertex="1" parent="prepBox">
          <mxGeometry x="30" y="120" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="prepArrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#1565c0;" edge="1" parent="prepBox" source="prep1" target="prep2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="prepArrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#1565c0;" edge="1" parent="prepBox" source="prep2" target="prep3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Training Loop -->
        <mxCell id="trainBox" value="Training Loop (for each epoch)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#fbc02d;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="320" y="20" width="520" height="320" as="geometry" />
        </mxCell>
        <!-- Forward Pass -->
        <mxCell id="forwardBox" value="Forward Pass" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;dashed=1;" vertex="1" parent="trainBox">
          <mxGeometry x="20" y="30" width="150" height="200" as="geometry" />
        </mxCell>
        <mxCell id="f1" value="Load Batch<br>(inputs, labels)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" vertex="1" parent="forwardBox">
          <mxGeometry x="15" y="10" width="120" height="36" as="geometry" />
        </mxCell>
        <mxCell id="f2" value="Zero Gradients" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" vertex="1" parent="forwardBox">
          <mxGeometry x="15" y="56" width="120" height="36" as="geometry" />
        </mxCell>
        <mxCell id="f3" value="Model Forward<br>Computing Outputs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" vertex="1" parent="forwardBox">
          <mxGeometry x="15" y="102" width="120" height="36" as="geometry" />
        </mxCell>
        <mxCell id="f4" value="Loss Computation<br>(Log Softmax)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" vertex="1" parent="forwardBox">
          <mxGeometry x="15" y="148" width="120" height="36" as="geometry" />
        </mxCell>
        <mxCell id="fArrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#388e3c;" edge="1" parent="forwardBox" source="f1" target="f2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fArrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#388e3c;" edge="1" parent="forwardBox" source="f2" target="f3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fArrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#388e3c;" edge="1" parent="forwardBox" source="f3" target="f4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Backward Pass -->
        <mxCell id="backwardBox" value="Backward Pass" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#8e24aa;dashed=1;" vertex="1" parent="trainBox">
          <mxGeometry x="200" y="30" width="150" height="200" as="geometry" />
        </mxCell>
        <mxCell id="b1" value="loss.backward()<br>Compute Gradients" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd0;strokeColor=#8e24aa;" vertex="1" parent="backwardBox">
          <mxGeometry x="15" y="10" width="120" height="36" as="geometry" />
        </mxCell>
        <mxCell id="b2" value="Manual Parameter Update<br>param ← lr * grad" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd0;strokeColor=#8e24aa;" vertex="1" parent="backwardBox">
          <mxGeometry x="15" y="56" width="120" height="36" as="geometry" />
        </mxCell>
        <mxCell id="b3" value="Update Running<br>Loss and Accuracy" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8bbd0;strokeColor=#8e24aa;" vertex="1" parent="backwardBox">
          <mxGeometry x="15" y="102" width="120" height="36" as="geometry" />
        </mxCell>
        <mxCell id="bArrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#8e24aa;" edge="1" parent="backwardBox" source="b1" target="b2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bArrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#8e24aa;" edge="1" parent="backwardBox" source="b2" target="b3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Evaluation -->
        <mxCell id="evalBox" value="Evaluation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#fbc02d;dashed=1;" vertex="1" parent="trainBox">
          <mxGeometry x="380" y="30" width="120" height="200" as="geometry" />
        </mxCell>
        <mxCell id="e1" value="Test Set Evaluation<br>model.eval()" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#fbc02d;" vertex="1" parent="evalBox">
          <mxGeometry x="10" y="10" width="100" height="36" as="geometry" />
        </mxCell>
        <mxCell id="e2" value="Compute Test<br>Loss and Accuracy" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#fbc02d;" vertex="1" parent="evalBox">
          <mxGeometry x="10" y="56" width="100" height="36" as="geometry" />
        </mxCell>
        <mxCell id="e3" value="Log Progress<br>(Print Metrics)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#fbc02d;" vertex="1" parent="evalBox">
          <mxGeometry x="10" y="102" width="100" height="36" as="geometry" />
        </mxCell>
        <mxCell id="eArrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#fbc02d;" edge="1" parent="evalBox" source="e1" target="e2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="eArrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#fbc02d;" edge="1" parent="evalBox" source="e2" target="e3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Training Loop Arrows -->
        <mxCell id="trainArrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#388e3c;" edge="1" parent="1" source="prep3" target="f1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="trainArrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#388e3c;" edge="1" parent="1" source="f4" target="b1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="trainArrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#8e24aa;" edge="1" parent="1" source="b3" target="e1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Final Evaluation and Visualization -->
        <mxCell id="finalBox" value="Final Evaluation and Visualization" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1565c0;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="380" width="1100" height="180" as="geometry" />
        </mxCell>
        <mxCell id="final1" value="Final Test Set Evaluation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1565c0;" vertex="1" parent="finalBox">
          <mxGeometry x="30" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="final2" value="Collect Predictions<br>and True Labels" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1565c0;" vertex="1" parent="finalBox">
          <mxGeometry x="210" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="final3" value="Plot Training and<br>Test Metrics" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1565c0;" vertex="1" parent="finalBox">
          <mxGeometry x="390" y="40" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="final4" value="Plot Confusion Matrix<br>and Examples" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#bbdefb;strokeColor=#1565c0;" vertex="1" parent="finalBox">
          <mxGeometry x="570" y="40" width="180" height="40" as="geometry" />
        </mxCell>
        <mxCell id="final5" value="Model Ready for Inference" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" vertex="1" parent="finalBox">
          <mxGeometry x="800" y="40" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="finalArrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#1565c0;" edge="1" parent="finalBox" source="final1" target="final2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="finalArrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#1565c0;" edge="1" parent="finalBox" source="final2" target="final3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="finalArrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#1565c0;" edge="1" parent="finalBox" source="final3" target="final4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="finalArrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#388e3c;" edge="1" parent="finalBox" source="final4" target="final5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Training Loop to Final Evaluation Arrow -->
        <mxCell id="toFinalArrow" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;endArrow=block;strokeColor=#388e3c;" edge="1" parent="1" source="e3" target="final1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

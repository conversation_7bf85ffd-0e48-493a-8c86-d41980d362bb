<mxfile host="65bd71144e">
    <diagram id="mermaid-graph" name="Page-1">
        <mxGraphModel dx="493" dy="611" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="1">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="g1" value="左翼 (预防)" style="swimlane;fillColor=#e6f7ff;strokeColor=#1890ff;fontSize=16;fontStyle=1;rounded=1;shadow=1;expand=1;collapsible=0;" parent="1" vertex="1">
                    <mxGeometry x="40" y="80" width="320" height="320" as="geometry"/>
                </mxCell>
                <mxCell id="T1" value="威胁1: 临时支撑失稳" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#1890ff;fontSize=14;fontStyle=1;shadow=1;" parent="g1" vertex="1">
                    <mxGeometry x="40" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="T2" value="威胁2: 吊装过程构件碰撞" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#1890ff;fontSize=14;fontStyle=1;shadow=1;" parent="g1" vertex="1">
                    <mxGeometry x="40" y="160" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="P1" value="屏障: 优化支撑体系设计" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f7ff;strokeColor=#1890ff;fontSize=14;shadow=1;" parent="g1" vertex="1">
                    <mxGeometry x="180" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="P2" value="屏障: BIM吊装路径模拟" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f7ff;strokeColor=#1890ff;fontSize=14;shadow=1;" parent="g1" vertex="1">
                    <mxGeometry x="180" y="160" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="e1" style="edgeStyle=elbowEdgeStyle;rounded=1;strokeColor=#1890ff;endArrow=block;endFill=1;" parent="g1" source="T1" target="P1" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e2" style="edgeStyle=elbowEdgeStyle;rounded=1;strokeColor=#1890ff;endArrow=block;endFill=1;" parent="g1" source="T2" target="P2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="g2" value="右翼 (缓解)" style="swimlane;fillColor=#fffbe6;strokeColor=#faad14;fontSize=16;fontStyle=1;rounded=1;shadow=1;collapsible=0;" parent="1" vertex="1">
                    <mxGeometry x="640" y="80" width="320" height="320" as="geometry"/>
                </mxCell>
                <mxCell id="C1" value="后果1: 人员伤亡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#faad14;fontSize=14;fontStyle=1;shadow=1;" parent="g2" vertex="1">
                    <mxGeometry x="40" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="C2" value="后果2: 结构损坏" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff;strokeColor=#faad14;fontSize=14;fontStyle=1;shadow=1;" parent="g2" vertex="1">
                    <mxGeometry x="40" y="160" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="M1" value="屏障: 扩大警戒区范围" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffbe6;strokeColor=#faad14;fontSize=14;shadow=1;" parent="g2" vertex="1">
                    <mxGeometry x="180" y="40" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="M2" value="屏障: 预设结构应力监测点" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffbe6;strokeColor=#faad14;fontSize=14;shadow=1;" parent="g2" vertex="1">
                    <mxGeometry x="180" y="160" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="e3" style="edgeStyle=elbowEdgeStyle;rounded=1;strokeColor=#faad14;endArrow=block;endFill=1;" parent="g2" source="C1" target="M1" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e4" style="edgeStyle=elbowEdgeStyle;rounded=1;strokeColor=#faad14;endArrow=block;endFill=1;" parent="g2" source="C2" target="M2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="Event" value="风险事件&lt;br&gt;悬挑屋盖安装失稳" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#cc0000;strokeWidth=2;fontSize=16;fontStyle=1;shadow=1;" parent="1" vertex="1">
                    <mxGeometry x="420" y="180" width="180" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="e5" style="edgeStyle=elbowEdgeStyle;rounded=1;strokeColor=#1890ff;endArrow=block;endFill=1;" parent="1" source="P1" target="Event" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e6" style="edgeStyle=elbowEdgeStyle;rounded=1;strokeColor=#1890ff;endArrow=block;endFill=1;" parent="1" source="P2" target="Event" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e7" style="edgeStyle=elbowEdgeStyle;rounded=1;strokeColor=#cc0000;endArrow=block;endFill=1;" parent="1" source="Event" target="C1" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="e8" style="edgeStyle=elbowEdgeStyle;rounded=1;strokeColor=#cc0000;endArrow=block;endFill=1;" parent="1" source="Event" target="C2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
<mxfile host="app.diagrams.net">
  <diagram id="PipingDiagram" name="P&ID Example">
    <mxGraphModel dx="1000" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- NG Source -->
        <mxCell id="2" value="NG\n20kPa" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0277bd;" vertex="1" parent="1">
          <mxGeometry x="40" y="120" width="60" height="40" as="geometry" />
        </mxCell>
        <!-- COG Source -->
        <mxCell id="3" value="COG" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;" vertex="1" parent="1">
          <mxGeometry x="40" y="220" width="60" height="40" as="geometry" />
        </mxCell>
        <!-- Mixer -->
        <mxCell id="4" value="混合器" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;" vertex="1" parent="1">
          <mxGeometry x="180" y="170" width="60" height="60" as="geometry" />
        </mxCell>
        <!-- Main Pipe -->
        <mxCell id="5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#616161;strokeWidth=3;" edge="1" parent="1" source="2" target="4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#616161;strokeWidth=3;" edge="1" parent="1" source="3" target="4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Flow Meter -->
        <mxCell id="7" value="FIC" style="shape=mxgraph.pid2inst.flow_indicator;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="110" y="140" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#616161;strokeWidth=2;" edge="1" parent="1" source="2" target="7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#616161;strokeWidth=2;" edge="1" parent="1" source="7" target="4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Valve -->
        <mxCell id="10" value="阀门" style="shape=mxgraph.pid2inst.valve;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="250" y="190" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#616161;strokeWidth=2;" edge="1" parent="1" source="4" target="10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Pressure Meter -->
        <mxCell id="12" value="PT" style="shape=mxgraph.pid2inst.pressure_indicator;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="310" y="200" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#616161;strokeWidth=2;" edge="1" parent="1" source="10" target="12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- To User (e.g. 炉/初扎) -->
        <mxCell id="14" value="用气点" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#fbc02d;" vertex="1" parent="1">
          <mxGeometry x="380" y="210" width="60" height="40" as="geometry" />
        </mxCell>
        <mxCell id="15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#616161;strokeWidth=2;" edge="1" parent="1" source="12" target="14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Control Signal (dashed line) -->
        <mxCell id="16" style="edgeStyle=orthogonalEdgeStyle;dashed=1;dashPattern=5 5;strokeColor=#1976d2;endArrow=blockThin;html=1;" edge="1" parent="1" source="7" target="10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

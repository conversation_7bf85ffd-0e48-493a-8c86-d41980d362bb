<mxfile host="app.diagrams.net">
  <diagram id="LayeredArchitecture" name="分层架构示意图-整理版">
    <mxGraphModel dx="1000" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="900" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- 业务层 -->
        <mxCell id="2" value="业务层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#bdbdbd;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="820" height="90" as="geometry" />
        </mxCell>
        <!-- 业务层子模块 -->
        <mxCell id="3" value="模型可视化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="2">
          <mxGeometry x="30" y="15" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4" value="模型管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="2">
          <mxGeometry x="240" y="15" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="5" value="用户管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="2">
          <mxGeometry x="450" y="15" width="180" height="60" as="geometry" />
        </mxCell>
        <!-- 业务层功能块 -->
        <mxCell id="6" value="数据分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="3">
          <mxGeometry x="20" y="15" width="65" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="报表生成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="3">
          <mxGeometry x="95" y="15" width="65" height="30" as="geometry" />
        </mxCell>
        <mxCell id="8" value="版本控制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="4">
          <mxGeometry x="20" y="15" width="65" height="30" as="geometry" />
        </mxCell>
        <mxCell id="9" value="模型监控" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="4">
          <mxGeometry x="95" y="15" width="65" height="30" as="geometry" />
        </mxCell>
        <mxCell id="10" value="登录注册" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#ffe082;" vertex="1" parent="5">
          <mxGeometry x="20" y="15" width="65" height="30" as="geometry" />
        </mxCell>
        <mxCell id="11" value="用户鉴权" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#ffe082;" vertex="1" parent="5">
          <mxGeometry x="95" y="15" width="65" height="30" as="geometry" />
        </mxCell>
        <!-- 模型层 -->
        <mxCell id="12" value="模型层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#bdbdbd;" vertex="1" parent="1">
          <mxGeometry x="40" y="150" width="820" height="110" as="geometry" />
        </mxCell>
        <!-- 模型层子模块 -->
        <mxCell id="13" value="数据驱动建模" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="12">
          <mxGeometry x="30" y="20" width="220" height="70" as="geometry" />
        </mxCell>
        <mxCell id="14" value="机理建模" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="12">
          <mxGeometry x="270" y="20" width="220" height="70" as="geometry" />
        </mxCell>
        <mxCell id="15" value="模型库建设" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="12">
          <mxGeometry x="510" y="20" width="220" height="70" as="geometry" />
        </mxCell>
        <!-- 模型层功能块 -->
        <mxCell id="16" value="XGBoost" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="13">
          <mxGeometry x="20" y="10" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="17" value="SVM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="13">
          <mxGeometry x="120" y="10" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="18" value="RF" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="13">
          <mxGeometry x="20" y="38" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="19" value="ELM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="13">
          <mxGeometry x="120" y="38" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="20" value="GA" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="14">
          <mxGeometry x="20" y="10" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="21" value="PSO" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="14">
          <mxGeometry x="120" y="10" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="22" value="ACO" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="14">
          <mxGeometry x="20" y="38" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="23" value="FPA" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="14">
          <mxGeometry x="120" y="38" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="24" value="模型训练" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#ffe082;" vertex="1" parent="15">
          <mxGeometry x="20" y="10" width="90" height="22" as="geometry" />
        </mxCell>
        <mxCell id="25" value="模型预测" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#ffe082;" vertex="1" parent="15">
          <mxGeometry x="120" y="10" width="90" height="22" as="geometry" />
        </mxCell>
        <mxCell id="26" value="增量学习" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#ffe082;" vertex="1" parent="15">
          <mxGeometry x="20" y="38" width="90" height="22" as="geometry" />
        </mxCell>
        <!-- 数据层 -->
        <mxCell id="27" value="数据层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#bdbdbd;" vertex="1" parent="1">
          <mxGeometry x="40" y="280" width="820" height="90" as="geometry" />
        </mxCell>
        <!-- 数据层子模块 -->
        <mxCell id="28" value="数据预处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="27">
          <mxGeometry x="30" y="15" width="320" height="60" as="geometry" />
        </mxCell>
        <mxCell id="29" value="数据协议" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="27">
          <mxGeometry x="370" y="15" width="180" height="60" as="geometry" />
        </mxCell>
        <!-- 数据层功能块 -->
        <mxCell id="30" value="数据采集存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="28">
          <mxGeometry x="20" y="15" width="90" height="22" as="geometry" />
        </mxCell>
        <mxCell id="31" value="数据清洗" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="28">
          <mxGeometry x="120" y="15" width="90" height="22" as="geometry" />
        </mxCell>
        <mxCell id="32" value="数据归约" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="28">
          <mxGeometry x="220" y="15" width="90" height="22" as="geometry" />
        </mxCell>
        <mxCell id="33" value="数据变换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="28">
          <mxGeometry x="20" y="43" width="90" height="22" as="geometry" />
        </mxCell>
        <mxCell id="34" value="OPC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="29">
          <mxGeometry x="20" y="15" width="70" height="22" as="geometry" />
        </mxCell>
        <mxCell id="35" value="MODBUS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="29">
          <mxGeometry x="100" y="15" width="70" height="22" as="geometry" />
        </mxCell>
        <!-- 边缘层 -->
        <mxCell id="36" value="边缘层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#bdbdbd;" vertex="1" parent="1">
          <mxGeometry x="40" y="390" width="820" height="90" as="geometry" />
        </mxCell>
        <!-- 边缘层子模块 -->
        <mxCell id="37" value="冷端设备接入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="36">
          <mxGeometry x="30" y="15" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="38" value="系统接入" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#bdbdbd;" vertex="1" parent="36">
          <mxGeometry x="270" y="15" width="220" height="60" as="geometry" />
        </mxCell>
        <!-- 边缘层功能块 -->
        <mxCell id="39" value="凝汽器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="37">
          <mxGeometry x="20" y="15" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="40" value="汽轮机" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="37">
          <mxGeometry x="120" y="15" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="41" value="循环水泵" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#90caf9;" vertex="1" parent="37">
          <mxGeometry x="20" y="43" width="80" height="22" as="geometry" />
        </mxCell>
        <mxCell id="42" value="DCS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="38">
          <mxGeometry x="20" y="15" width="60" height="22" as="geometry" />
        </mxCell>
        <mxCell id="43" value="SIS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="38">
          <mxGeometry x="100" y="15" width="60" height="22" as="geometry" />
        </mxCell>
        <mxCell id="44" value="边缘计算设备" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#a5d6a7;" vertex="1" parent="38">
          <mxGeometry x="180" y="15" width="100" height="22" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

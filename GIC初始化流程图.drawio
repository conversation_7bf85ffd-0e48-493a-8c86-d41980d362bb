<mxfile host="app.diagrams.net">
  <diagram id="gic_init_flow" name="GIC初始化流程图">
    <mxGraphModel dx="1200" dy="700" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="700" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- 链接脚本中定义段 -->
        <mxCell id="ld_script" value="vm linux.lds: __irqchip_of_table" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="120" y="30" width="180" height="36" as="geometry" />
        </mxCell>
        <!-- 声明结构体 -->
        <mxCell id="irqchip_decl" value="IRQCHIP_DECLARE(gic_400, 'arm,gic-400', gic_of_init)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="420" y="30" width="320" height="36" as="geometry" />
        </mxCell>
        <!-- of_irq_init -->
        <mxCell id="of_irq_init" value="of_irq_init(__irqchip_of_table)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#fbc02d;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="120" y="110" width="180" height="36" as="geometry" />
        </mxCell>
        <!-- gic_of_init -->
        <mxCell id="gic_of_init" value="gic_of_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="320" y="200" width="120" height="40" as="geometry" />
        </mxCell>
        <!-- 解析节点、回调 -->
        <mxCell id="desc_parse" value="desc->irq_init_cb=match->data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;" vertex="1" parent="1">
          <mxGeometry x="120" y="170" width="180" height="32" as="geometry" />
        </mxCell>
        <mxCell id="desc_call" value="desc->irq_init_cb(desc->dev, desc->interrupt_parent)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;" vertex="1" parent="1">
          <mxGeometry x="120" y="210" width="180" height="32" as="geometry" />
        </mxCell>
        <!-- gic_of_setup -->
        <mxCell id="gic_of_setup" value="gic_of_setup" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" vertex="1" parent="1">
          <mxGeometry x="480" y="260" width="120" height="32" as="geometry" />
        </mxCell>
        <!-- gic_init_bases -->
        <mxCell id="gic_init_bases" value="gic_init_bases" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" vertex="1" parent="1">
          <mxGeometry x="640" y="260" width="120" height="32" as="geometry" />
        </mxCell>
        <!-- gic_init_chip -->
        <mxCell id="gic_init_chip" value="gic_init_chip" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;" vertex="1" parent="1">
          <mxGeometry x="800" y="260" width="120" height="32" as="geometry" />
        </mxCell>
        <!-- gic_irq_domain_hierarchy_ops -->
        <mxCell id="gic_irq_domain_hierarchy_ops" value="gic_irq_domain_hierarchy_ops" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;" vertex="1" parent="1">
          <mxGeometry x="960" y="260" width="180" height="32" as="geometry" />
        </mxCell>
        <!-- gic_cpu_init -->
        <mxCell id="gic_cpu_init" value="gic_cpu_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#8e24aa;" vertex="1" parent="1">
          <mxGeometry x="640" y="320" width="120" height="32" as="geometry" />
        </mxCell>
        <!-- gic_pm_init -->
        <mxCell id="gic_pm_init" value="gic_pm_init" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#8e24aa;" vertex="1" parent="1">
          <mxGeometry x="800" y="320" width="120" height="32" as="geometry" />
        </mxCell>
        <!-- gic_raise_softirq -->
        <mxCell id="gic_raise_softirq" value="gic_raise_softirq" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#fbc02d;" vertex="1" parent="1">
          <mxGeometry x="480" y="320" width="120" height="32" as="geometry" />
        </mxCell>
        <!-- gic_starting_cpu -->
        <mxCell id="gic_starting_cpu" value="gic_starting_cpu" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#fbc02d;" vertex="1" parent="1">
          <mxGeometry x="480" y="370" width="120" height="32" as="geometry" />
        </mxCell>
        <!-- gic_handle_irq -->
        <mxCell id="gic_handle_irq" value="gic_handle_irq" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#fbc02d;" vertex="1" parent="1">
          <mxGeometry x="640" y="370" width="120" height="32" as="geometry" />
        </mxCell>
        <!-- 箭头和连线 -->
        <mxCell id="a1" style="edgeStyle=orthogonalEdgeStyle;dashed=1;dashPattern=5 5;html=1;strokeColor=#d32f2f;endArrow=block;" edge="1" parent="1" source="ld_script" target="of_irq_init">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a2" style="edgeStyle=orthogonalEdgeStyle;dashed=1;dashPattern=5 5;html=1;strokeColor=#1976d2;endArrow=block;" edge="1" parent="1" source="irqchip_decl" target="gic_of_init">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a3" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#616161;endArrow=block;" edge="1" parent="1" source="of_irq_init" target="desc_parse">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a4" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#616161;endArrow=block;" edge="1" parent="1" source="desc_parse" target="desc_call">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a5" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#616161;endArrow=block;" edge="1" parent="1" source="desc_call" target="gic_of_init">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a6" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#f57c00;endArrow=block;" edge="1" parent="1" source="gic_of_init" target="gic_of_setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a7" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" edge="1" parent="1" source="gic_of_setup" target="gic_init_bases">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a8" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" edge="1" parent="1" source="gic_init_bases" target="gic_init_chip">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a9" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" edge="1" parent="1" source="gic_init_chip" target="gic_irq_domain_hierarchy_ops">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a10" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" edge="1" parent="1" source="gic_init_bases" target="gic_cpu_init">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a11" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#388e3c;endArrow=block;" edge="1" parent="1" source="gic_init_bases" target="gic_pm_init">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a12" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#fbc02d;endArrow=block;" edge="1" parent="1" source="gic_of_setup" target="gic_raise_softirq">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a13" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#fbc02d;endArrow=block;" edge="1" parent="1" source="gic_of_setup" target="gic_starting_cpu">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a14" style="edgeStyle=orthogonalEdgeStyle;html=1;strokeColor=#fbc02d;endArrow=block;" edge="1" parent="1" source="gic_of_setup" target="gic_handle_irq">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
